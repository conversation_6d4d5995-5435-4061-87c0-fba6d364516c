"use client";

import { adminApi } from "@/lib/api-client";
import { Service, ServiceInsert } from "@/lib/types";
import { AlertCircle, Clock, Edit, Loader2, Plus, Save, Tag, Trash2, Users, X } from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

interface ServiceWithPricing extends Service {
	pricing_tiers?: any[];
	id: string;
	name: string;
	description: string | null;
	duration_minutes: number;
	base_price: number;
	max_participants: number;
	category: string | null;
	image_url: string | null;
	features: string[] | null;
	is_active: boolean;
}

const AdminServices = () => {
	const [services, setServices] = useState<ServiceWithPricing[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isEditing, setIsEditing] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [editForm, setEditForm] = useState<Partial<ServiceInsert>>({});
	const [saving, setSaving] = useState(false);

	useEffect(() => {
		console.log('AdminServices useEffect triggered');
		fetchServices();
	}, []);

	const fetchServices = async () => {
		try {
			console.log('fetchServices called');
			setLoading(true);
			setError(null);
			console.log('About to call adminApi.getServices()');
			const response = await adminApi.getServices();
			console.log('Services API response:', response);
			if (response?.services) {
				console.log('Setting services:', response.services);
				setServices(response.services);
			} else {
				console.log('No services found in response');
			}
		} catch (err) {
			console.error('Error fetching services:', err);
			setError('Erreur lors du chargement des services');
		} finally {
			console.log('Setting loading to false');
			setLoading(false);
		}
	};

	const handleEdit = (service: ServiceWithPricing) => {
		setIsEditing(service.id);
		setEditForm({
			name: service.name,
			description: service.description,
			duration_minutes: service.duration_minutes,
			base_price: service.base_price,
			max_participants: service.max_participants,
			category: service.category,
			image_url: service.image_url,
			features: service.features,
			is_active: service.is_active
		});
	};

	const handleCreate = () => {
		setIsCreating(true);
		setEditForm({
			name: "",
			description: "",
			duration_minutes: 120,
			base_price: 0,
			max_participants: 10,
			category: "",
			image_url: "",
			features: [],
			is_active: true
		});
	};

	const handleSave = async () => {
		if (!editForm.name || !editForm.duration_minutes || !editForm.base_price || !editForm.max_participants) {
			setError('Veuillez remplir tous les champs obligatoires');
			return;
		}

		try {
			setSaving(true);
			setError(null);

			if (isCreating) {
				await adminApi.createService(editForm);
				setIsCreating(false);
			} else if (isEditing) {
				await adminApi.updateService(isEditing, editForm);
				setIsEditing(null);
			}

			setEditForm({});
			await fetchServices(); // Refresh the list
		} catch (err) {
			console.error('Error saving service:', err);
			setError('Erreur lors de la sauvegarde du service');
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		setIsEditing(null);
		setIsCreating(false);
		setEditForm({});
		setError(null);
	};

	const handleDelete = async (serviceId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer ce service ?")) {
			try {
				await adminApi.deleteService(serviceId);
				await fetchServices(); // Refresh the list
			} catch (err) {
				console.error('Error deleting service:', err);
				setError('Erreur lors de la suppression du service');
			}
		}
	};

	const handleInputChange = (field: keyof ServiceInsert, value: any) => {
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, [field]: value }));
	};

	const addFeature = () => {
		const features = editForm.features || [];
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, features: [...features, ""] }));
	};

	const updateFeature = (index: number, value: string) => {
		const features = [...(editForm.features || [])];
		features[index] = value;
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, features }));
	};

	const removeFeature = (index: number) => {
		const features = [...(editForm.features || [])];
		features.splice(index, 1);
		setEditForm((prev: Partial<ServiceInsert>) => ({ ...prev, features }));
	};

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Services</h1>
					<p className="text-gray-600">Gérez vos excursions et activités</p>
				</div>
				<Button onClick={handleCreate} icon={Plus}>
					Nouveau Service
				</Button>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Loading State */}
			{loading && (
				<div className="flex items-center justify-center h-64">
					<div className="flex items-center space-x-2">
						<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
						<span className="text-gray-600">Chargement des services...</span>
					</div>
				</div>
			)}

			{/* Create/Edit Form */}
			{(isCreating || isEditing) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un nouveau service" : "Modifier le service"}
					</h2>

					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Nom du service *</label>
								<input
									type="text"
									value={editForm.name || ""}
									onChange={(e) => handleInputChange("name", e.target.value)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Description *
								</label>
								<textarea
									rows={4}
									value={editForm.description || ""}
									onChange={(e) => handleInputChange("description", e.target.value)}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Prix de base (€) *</label>
									<input
										type="number"
										step="0.01"
										value={editForm.base_price || 0}
										onChange={(e) => handleInputChange("base_price", parseFloat(e.target.value))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Durée (minutes) *
									</label>
									<input
										type="number"
										value={editForm.duration_minutes || 120}
										onChange={(e) => handleInputChange("duration_minutes", parseInt(e.target.value))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Catégorie *</label>
									<input
										type="text"
										value={editForm.category || ""}
										onChange={(e) => handleInputChange("category", e.target.value)}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">
										Participants max *
									</label>
									<input
										type="number"
										value={editForm.max_participants || 10}
										onChange={(e) => handleInputChange("max_participants", parseInt(e.target.value))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									<input
										type="checkbox"
										checked={editForm.is_active ?? true}
										onChange={(e) => handleInputChange("is_active", e.target.checked)}
										className="mr-2"
									/>
									Service actif
								</label>
							</div>
						</div>

						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">URL de l'image</label>
								<input
									type="url"
									value={editForm.image_url || ""}
									onChange={(e) => handleInputChange("image_url", e.target.value)}
									placeholder="https://example.com/image.jpg"
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<div className="flex justify-between items-center mb-2">
									<label className="block text-sm font-medium text-gray-700">
										Caractéristiques incluses
									</label>
									<button
										onClick={addFeature}
										className="text-emerald-600 hover:text-emerald-700 text-sm font-medium"
									>
										+ Ajouter
									</button>
								</div>
								<div className="space-y-2">
									{(editForm.features || []).map((feature: string, index: number) => (
										<div key={index} className="flex gap-2">
											<input
												type="text"
												value={feature}
												onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateFeature(index, e.target.value)}
												className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
												placeholder="Ex: Guide expert"
											/>
											<button
												onClick={() => removeFeature(index)}
												className="p-2 text-red-600 hover:text-red-700"
											>
												<X className="h-4 w-4" />
											</button>
										</div>
									))}
								</div>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Règles de disponibilité
								</label>
								<div className="space-y-3 p-4 bg-gray-50 rounded-lg">
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Temps de préparation</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>30 minutes</option>
											<option>1 heure</option>
											<option>2 heures</option>
										</select>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Réservation minimum</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>24 heures</option>
											<option>48 heures</option>
											<option>72 heures</option>
										</select>
									</div>
									<div className="flex items-center justify-between">
										<span className="text-sm text-gray-700">Annulation gratuite</span>
										<select className="px-3 py-1 border border-gray-300 rounded text-sm">
											<option>24 heures</option>
											<option>48 heures</option>
											<option>72 heures</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel} icon={X} disabled={saving}>
							Annuler
						</Button>
						<Button onClick={handleSave} icon={saving ? Loader2 : Save} disabled={saving}>
							{saving ? "Sauvegarde..." : (isCreating ? "Créer le service" : "Sauvegarder")}
						</Button>
					</div>
				</div>
			)}

			{/* Services List */}
			{!loading && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200">
					<div className="p-6 border-b border-gray-200">
						<h2 className="text-xl font-bold text-gray-900">Services ({services.length})</h2>
					</div>
					<div className="divide-y divide-gray-200">
						{services.length === 0 ? (
							<div className="p-12 text-center">
								<p className="text-gray-500">Aucun service trouvé</p>
							</div>
						) : (
							services.map((service) => (
								<div key={service.id} className="p-6 flex items-center gap-4">
									<img
										src={service.image_url || "/placeholder-service.jpg"}
										alt={service.name}
										className="w-16 h-16 object-cover rounded-lg"
										onError={(e) => {
											e.currentTarget.src = "/placeholder-service.jpg";
										}}
									/>
									<div className="flex-1">
										<div className="flex items-center gap-2">
											<h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
											{!service.is_active && (
												<span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-full">
													Inactif
												</span>
											)}
										</div>
										<p className="text-gray-600 text-sm mt-1 line-clamp-2">
											{service.description || "Aucune description"}
										</p>
										<div className="flex items-center gap-6 mt-3 text-sm text-gray-500">
											<span className="flex items-center gap-1">
												<Clock className="h-4 w-4" />
												{service.duration_minutes} min
											</span>
											<span className="flex items-center gap-1">
												<Users className="h-4 w-4" />
												{service.max_participants} pers.
											</span>
											<span className="flex items-center gap-1">
												<Tag className="h-4 w-4" />
												{service.category || "Non catégorisé"}
											</span>
											<span className="text-2xl font-bold text-emerald-600">
												€{service.base_price?.toFixed(2) || "0.00"}
											</span>
										</div>
									</div>
									<div className="flex gap-2">
										<Button variant="outline" size="sm" icon={Edit} onClick={() => handleEdit(service)}>
											Modifier
										</Button>
										<Button
											variant="outline"
											size="sm"
											icon={Trash2}
											onClick={() => handleDelete(service.id)}
											className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
											disabled={saving}
										>
											Supprimer
										</Button>
									</div>
								</div>
							))
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminServices;
