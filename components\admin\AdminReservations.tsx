"use client";

import { adminApi } from "@/lib/api-client";
import { AlertCircle, Eye, Loader2, Mail, Phone, Search, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import But<PERSON> from "./ui/Button";

interface ReservationData {
	id: string;
	reservation_number: string;
	status: string;
	start_time: string;
	end_time: string;
	participant_count: number;
	total_amount: number;
	special_requests?: string;
	admin_notes?: string;
	service?: {
		id: string;
		name: string;
		category?: string;
		image_url?: string;
	};
	customer_profile?: {
		id: string;
		first_name?: string;
		last_name?: string;
		email?: string;
		phone?: string;
	};
	assigned_employee?: {
		id: string;
		first_name: string;
		last_name: string;
	};
	payments?: any[];
	customer_feedback?: any[];
}

const AdminReservations = () => {
	const [reservations, setReservations] = useState<ReservationData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedReservation, setSelectedReservation] = useState<ReservationData | null>(null);
	const [filterStatus, setFilterStatus] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");
	const [updating, setUpdating] = useState<string | null>(null);

	useEffect(() => {
		fetchReservations();
	}, []);

	const fetchReservations = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getReservations({
				limit: 50,
				page: 1
			});
			if (response?.reservations) {
				setReservations(response.reservations);
			}
		} catch (err) {
			console.error('Error fetching reservations:', err);
			setError('Erreur lors du chargement des réservations');
		} finally {
			setLoading(false);
		}
	};

	const filteredReservations = reservations.filter((reservation) => {
		const matchesStatus = filterStatus === "all" || reservation.status === filterStatus;
		const customerName = `${reservation.customer_profile?.first_name || ''} ${reservation.customer_profile?.last_name || ''}`.trim();
		const serviceName = reservation.service?.name || '';
		const matchesSearch =
			searchTerm === "" ||
			customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			serviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
			reservation.reservation_number.toLowerCase().includes(searchTerm.toLowerCase());

		return matchesStatus && matchesSearch;
	});

	const handleViewDetails = (reservation: ReservationData) => {
		setSelectedReservation(reservation);
	};

	const handleStatusChange = async (reservationId: string, newStatus: "confirmed" | "pending" | "cancelled") => {
		try {
			setUpdating(reservationId);
			setError(null);
			await adminApi.updateReservation(reservationId, { status: newStatus });
			await fetchReservations(); // Refresh the list
		} catch (err) {
			console.error('Error updating reservation status:', err);
			setError('Erreur lors de la mise à jour du statut');
		} finally {
			setUpdating(null);
		}
	};

	const handleDelete = async (reservationId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir annuler cette réservation ?")) {
			try {
				setUpdating(reservationId);
				setError(null);
				await adminApi.cancelReservation(reservationId, { reason: 'Annulation administrative' });
				await fetchReservations(); // Refresh the list
			} catch (err) {
				console.error('Error cancelling reservation:', err);
				setError('Erreur lors de l\'annulation de la réservation');
			} finally {
				setUpdating(null);
			}
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "confirmed":
				return "bg-green-100 text-green-800";
			case "pending":
				return "bg-yellow-100 text-yellow-800";
			case "cancelled":
				return "bg-red-100 text-red-800";
			case "completed":
				return "bg-blue-100 text-blue-800";
			case "no_show":
				return "bg-gray-100 text-gray-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "confirmed":
				return "Confirmé";
			case "pending":
				return "En attente";
			case "cancelled":
				return "Annulé";
			case "completed":
				return "Terminé";
			case "no_show":
				return "Absent";
			default:
				return status;
		}
	};

	return (
		<div className="p-6">
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Réservations</h1>
				<p className="text-gray-600">Gérez toutes les réservations de vos clients</p>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Loading State */}
			{loading && (
				<div className="flex items-center justify-center h-64">
					<div className="flex items-center space-x-2">
						<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
						<span className="text-gray-600">Chargement des réservations...</span>
					</div>
				</div>
			)}

			{/* Filters */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
				<div className="flex flex-col lg:flex-row gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
						<input
							type="text"
							placeholder="Rechercher par nom de client ou service..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						/>
					</div>

					<div className="flex gap-2">
						<select
							value={filterStatus}
							onChange={(e) => setFilterStatus(e.target.value)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="all">Tous les statuts</option>
							<option value="confirmed">Confirmé</option>
							<option value="pending">En attente</option>
							<option value="cancelled">Annulé</option>
							<option value="completed">Terminé</option>
							<option value="no_show">Absent</option>
						</select>
					</div>
				</div>
			</div>

			{/* Reservations Table */}
			{!loading && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
				<div className="p-6 border-b border-gray-200">
					<h2 className="text-xl font-bold text-gray-900">Réservations ({filteredReservations.length})</h2>
				</div>

				<div className="overflow-x-auto">
					<table className="w-full">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Client
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Service
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Date & Heure
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Participants
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Montant
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Statut
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredReservations.length === 0 ? (
								<tr>
									<td colSpan={7} className="px-6 py-12 text-center">
										<p className="text-gray-500">Aucune réservation trouvée</p>
									</td>
								</tr>
							) : (
								filteredReservations.map((reservation) => {
									const customerName = `${reservation.customer_profile?.first_name || ''} ${reservation.customer_profile?.last_name || ''}`.trim() || 'Client inconnu';
									const isUpdating = updating === reservation.id;

									return (
										<tr key={reservation.id} className="hover:bg-gray-50">
											<td className="px-6 py-4 whitespace-nowrap">
												<div>
													<div className="text-sm font-medium text-gray-900">
														{customerName}
													</div>
													<div className="text-sm text-gray-500">{reservation.customer_profile?.email}</div>
												</div>
											</td>
											<td className="px-6 py-4 whitespace-nowrap">
												<div className="text-sm text-gray-900">{reservation.service?.name || 'Service inconnu'}</div>
											</td>
											<td className="px-6 py-4 whitespace-nowrap">
												<div className="text-sm text-gray-900">
													{new Date(reservation.start_time).toLocaleDateString("fr-FR")}
												</div>
												<div className="text-sm text-gray-500">
													{new Date(reservation.start_time).toLocaleTimeString("fr-FR", { hour: '2-digit', minute: '2-digit' })}
												</div>
											</td>
											<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
												{reservation.participant_count}
											</td>
											<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
												€{reservation.total_amount?.toFixed(2) || '0.00'}
											</td>
											<td className="px-6 py-4 whitespace-nowrap">
												<select
													value={reservation.status}
													onChange={(e) => handleStatusChange(reservation.id, e.target.value as any)}
													className={`px-2 py-1 text-xs font-medium rounded-full border-0 ${getStatusColor(
														reservation.status
													)}`}
													disabled={isUpdating}
												>
													<option value="pending">En attente</option>
													<option value="confirmed">Confirmé</option>
													<option value="cancelled">Annulé</option>
													<option value="completed">Terminé</option>
												</select>
											</td>
											<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
												<div className="flex gap-2">
													<button
														onClick={() => handleViewDetails(reservation)}
														className="text-emerald-600 hover:text-emerald-900"
														disabled={isUpdating}
													>
														<Eye className="h-4 w-4" />
													</button>
													<button
														onClick={() => handleDelete(reservation.id)}
														className="text-red-600 hover:text-red-900"
														disabled={isUpdating}
													>
														{isUpdating ? <Loader2 className="h-4 w-4 animate-spin" /> : <Trash2 className="h-4 w-4" />}
													</button>
												</div>
											</td>
										</tr>
									);
								})
							)}
						</tbody>
					</table>
				</div>
			</div>
			)}

			{/* Reservation Details Modal */}
			{selectedReservation && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
						<div className="p-6 border-b border-gray-200">
							<div className="flex justify-between items-center">
								<h2 className="text-2xl font-bold text-gray-900">Détails de la réservation</h2>
								<button
									onClick={() => setSelectedReservation(null)}
									className="text-gray-400 hover:text-gray-600"
								>
									✕
								</button>
							</div>
						</div>

						<div className="p-6 space-y-6">
							{/* Customer Information */}
							<div>
								<h3 className="text-lg font-semibold text-gray-900 mb-3">Informations client</h3>
								<div className="bg-gray-50 rounded-lg p-4 space-y-2">
									<div className="flex justify-between">
										<span className="text-gray-600">Nom:</span>
										<span className="font-medium">
											{`${selectedReservation.customer_profile?.first_name || ''} ${selectedReservation.customer_profile?.last_name || ''}`.trim() || 'Client inconnu'}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Email:</span>
										<span className="font-medium">{selectedReservation.customer_profile?.email || 'Non renseigné'}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Téléphone:</span>
										<span className="font-medium">{selectedReservation.customer_profile?.phone || 'Non renseigné'}</span>
									</div>
								</div>
							</div>

							{/* Reservation Details */}
							<div>
								<h3 className="text-lg font-semibold text-gray-900 mb-3">Détails de la réservation</h3>
								<div className="bg-gray-50 rounded-lg p-4 space-y-2">
									<div className="flex justify-between">
										<span className="text-gray-600">N° Réservation:</span>
										<span className="font-medium">{selectedReservation.reservation_number}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Service:</span>
										<span className="font-medium">{selectedReservation.service?.name || 'Service inconnu'}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Date:</span>
										<span className="font-medium">
											{new Date(selectedReservation.start_time).toLocaleDateString("fr-FR")}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Heure:</span>
										<span className="font-medium">
											{new Date(selectedReservation.start_time).toLocaleTimeString("fr-FR", { hour: '2-digit', minute: '2-digit' })}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Participants:</span>
										<span className="font-medium">{selectedReservation.participant_count}</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Prix total:</span>
										<span className="font-medium text-emerald-600">
											€{selectedReservation.total_amount?.toFixed(2) || '0.00'}
										</span>
									</div>
									<div className="flex justify-between">
										<span className="text-gray-600">Statut:</span>
										<span
											className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
												selectedReservation.status
											)}`}
										>
											{getStatusText(selectedReservation.status)}
										</span>
									</div>
								</div>
							</div>

							{/* Actions */}
							<div className="flex flex-col sm:flex-row gap-3">
								{selectedReservation.customer_profile?.email && (
									<Button
										onClick={() => window.open(`mailto:${selectedReservation.customer_profile?.email}`)}
										className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700"
									>
										<Mail className="h-4 w-4" />
										Envoyer un email
									</Button>
								)}
								{selectedReservation.customer_profile?.phone && (
									<Button
										onClick={() => window.open(`tel:${selectedReservation.customer_profile?.phone}`)}
										className="flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700"
									>
										<Phone className="h-4 w-4" />
										Appeler
									</Button>
								)}
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminReservations;
