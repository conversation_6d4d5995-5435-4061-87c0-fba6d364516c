import { supabase } from './supabase'

interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  body?: any
  headers?: Record<string, string>
}

class ApiClient {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NODE_ENV === 'development' 
      ? 'http://localhost:3000' 
      : window.location.origin
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const { data: { session } } = await supabase.auth.getSession()
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (session?.access_token) {
      headers['Authorization'] = `Bearer ${session.access_token}`
    }

    return headers
  }

  async request<T = any>(endpoint: string, options: ApiRequestOptions = {}): Promise<T> {
    const { method = 'GET', body, headers: customHeaders = {} } = options

    const authHeaders = await this.getAuthHeaders()
    const headers = { ...authHeaders, ...customHeaders }

    const config: RequestInit = {
      method,
      headers,
    }

    if (body && method !== 'GET') {
      config.body = JSON.stringify(body)
    }

    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`
    
    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      // Handle empty responses
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        return await response.json()
      }
      
      return response.text() as any
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', headers })
  }

  async post<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body, headers })
  }

  async put<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body, headers })
  }

  async patch<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'PATCH', body, headers })
  }

  async delete<T = any>(endpoint: string, body?: any, headers?: Record<string, string>): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', body, headers })
  }
}

// Create singleton instance
export const apiClient = new ApiClient()

// Admin API specific methods
export const adminApi = {
  // Services
  getServices: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : ''
    return apiClient.get(`/api/admin/services${query}`)
  },
  
  getService: (id: string) => apiClient.get(`/api/admin/services/${id}`),
  
  createService: (data: any) => apiClient.post('/api/admin/services', data),
  
  updateService: (id: string, data: any) => apiClient.put(`/api/admin/services/${id}`, data),
  
  deleteService: (id: string) => apiClient.delete(`/api/admin/services/${id}`),

  // Reservations
  getReservations: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : ''
    return apiClient.get(`/api/admin/reservations${query}`)
  },
  
  getReservation: (id: string) => apiClient.get(`/api/admin/reservations/${id}`),
  
  updateReservation: (id: string, data: any) => apiClient.put(`/api/admin/reservations/${id}`, data),
  
  cancelReservation: (id: string, data?: any) => apiClient.delete(`/api/admin/reservations/${id}`, data),

  // Customers
  getCustomers: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : ''
    return apiClient.get(`/api/admin/customers${query}`)
  },
  
  getCustomer: (id: string) => apiClient.get(`/api/admin/customers/${id}`),
  
  updateCustomer: (id: string, data: any) => apiClient.put(`/api/admin/customers/${id}`, data),

  // Employees
  getEmployees: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : ''
    return apiClient.get(`/api/admin/employees${query}`)
  },
  
  getEmployee: (id: string) => apiClient.get(`/api/admin/employees/${id}`),
  
  createEmployee: (data: any) => apiClient.post('/api/admin/employees', data),
  
  updateEmployee: (id: string, data: any) => apiClient.put(`/api/admin/employees/${id}`, data),

  // Equipment
  getEquipment: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : ''
    return apiClient.get(`/api/admin/equipment${query}`)
  },
  
  getEquipmentItem: (id: string) => apiClient.get(`/api/admin/equipment/${id}`),
  
  createEquipment: (data: any) => apiClient.post('/api/admin/equipment', data),
  
  updateEquipment: (id: string, data: any) => apiClient.put(`/api/admin/equipment/${id}`, data),

  // Analytics
  getAnalytics: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : ''
    return apiClient.get(`/api/admin/analytics${query}`)
  },
  
  populateAnalytics: (data?: any) => apiClient.post('/api/admin/analytics/populate', data),

  // Auth
  verifyAuth: () => apiClient.get('/api/auth/admin/verify')
}

export default apiClient
