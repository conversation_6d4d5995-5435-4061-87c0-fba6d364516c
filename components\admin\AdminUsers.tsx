"use client";

import { adminApi } from "@/lib/api-client";
import { AlertCircle, Edit, Loader2, Mail, Plus, Search, Shield, Trash2, User, UserCheck } from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

interface EmployeeData {
	id: string;
	first_name: string;
	last_name: string;
	email: string;
	phone?: string;
	role: string;
	qualifications?: string[];
	availability_pattern?: any;
	hourly_rate?: number;
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

const AdminUsers = () => {
	const [employees, setEmployees] = useState<EmployeeData[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [isEditing, setIsEditing] = useState<string | null>(null);
	const [searchTerm, setSearchTerm] = useState("");
	const [userForm, setUserForm] = useState<Partial<EmployeeData>>({});
	const [saving, setSaving] = useState(false);

	useEffect(() => {
		fetchEmployees();
	}, []);

	const fetchEmployees = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getEmployees({
				limit: 100,
				page: 1
			});
			if (response?.data) {
				setEmployees(response.data);
			}
		} catch (err) {
			console.error('Error fetching employees:', err);
			setError('Erreur lors du chargement des employés');
		} finally {
			setLoading(false);
		}
	};

	const getFullName = (employee: EmployeeData) => {
		return `${employee.first_name} ${employee.last_name}`.trim();
	};

	const filteredUsers = employees.filter(
		(employee) => {
			const fullName = getFullName(employee);
			return fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
				employee.role.toLowerCase().includes(searchTerm.toLowerCase());
		}
	);

	const handleCreate = () => {
		setIsCreating(true);
		setUserForm({
			first_name: "",
			last_name: "",
			email: "",
			phone: "",
			role: "guide",
			qualifications: [],
			hourly_rate: 0,
			is_active: true,
		});
	};

	const handleEdit = (employee: EmployeeData) => {
		setIsEditing(employee.id);
		setUserForm(employee);
	};

	const handleSave = async () => {
		if (!userForm.first_name || !userForm.last_name || !userForm.email || !userForm.role) {
			setError('Veuillez remplir tous les champs obligatoires');
			return;
		}

		try {
			setSaving(true);
			setError(null);

			if (isCreating) {
				await adminApi.createEmployee(userForm);
				setIsCreating(false);
			} else if (isEditing) {
				await adminApi.updateEmployee(isEditing, userForm);
				setIsEditing(null);
			}

			setUserForm({});
			await fetchEmployees(); // Refresh the list
		} catch (err) {
			console.error('Error saving employee:', err);
			setError('Erreur lors de la sauvegarde de l\'employé');
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		setIsCreating(false);
		setIsEditing(null);
		setUserForm({});
		setError(null);
	};

	const handleDelete = async (employeeId: string) => {
		if (window.confirm("Êtes-vous sûr de vouloir supprimer cet employé ?")) {
			try {
				await adminApi.deleteEmployee(employeeId);
				await fetchEmployees(); // Refresh the list
			} catch (err) {
				console.error('Error deleting employee:', err);
				setError('Erreur lors de la suppression de l\'employé');
			}
		}
	};

	const handleInvite = () => {
		alert("Invitation envoyée avec succès !");
	};

	const getRoleIcon = (role: string) => {
		switch (role) {
			case "admin":
				return <Shield className="h-5 w-5 text-red-600" />;
			case "manager":
				return <UserCheck className="h-5 w-5 text-blue-600" />;
			default:
				return <User className="h-5 w-5 text-gray-600" />;
		}
	};

	const getRoleColor = (role: string) => {
		switch (role) {
			case "manager":
				return "bg-red-100 text-red-800";
			case "captain":
				return "bg-blue-100 text-blue-800";
			case "instructor":
				return "bg-purple-100 text-purple-800";
			case "guide":
				return "bg-green-100 text-green-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const getRoleText = (role: string) => {
		switch (role) {
			case "manager":
				return "Manager";
			case "captain":
				return "Capitaine";
			case "instructor":
				return "Instructeur";
			case "guide":
				return "Guide";
			default:
				return role;
		}
	};

	const getPermissionsList = (role: string) => {
		switch (role) {
			case "admin":
				return ["Tous les droits"];
			case "manager":
				return ["Réservations", "Services", "Employés"];
			case "employee":
				return ["Consultation uniquement"];
			default:
				return [];
		}
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Employés</h1>
					<p className="text-gray-600">Gérez votre équipe et leurs qualifications</p>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="flex items-center space-x-2">
						<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
						<span className="text-gray-600">Chargement des employés...</span>
					</div>
				</div>
			</div>
		)
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Gestion des Employés</h1>
					<p className="text-gray-600">Gérez votre équipe et leurs qualifications</p>
				</div>
				<div className="flex gap-4">
					<Button variant="outline" onClick={handleInvite} icon={Mail}>
						Inviter un utilisateur
					</Button>
					<Button onClick={handleCreate} icon={Plus}>
						Nouvel Utilisateur
					</Button>
				</div>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Search */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
				<div className="relative">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
					<input
						type="text"
						placeholder="Rechercher un utilisateur..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
					/>
				</div>
			</div>

			{/* Create/Edit Form */}
			{(isCreating || isEditing) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un nouvel utilisateur" : "Modifier l'utilisateur"}
					</h2>

					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Prénom *</label>
									<input
										type="text"
										value={userForm.first_name || ""}
										onChange={(e) => setUserForm((prev) => ({ ...prev, first_name: e.target.value }))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-2">Nom *</label>
									<input
										type="text"
										value={userForm.last_name || ""}
										onChange={(e) => setUserForm((prev) => ({ ...prev, last_name: e.target.value }))}
										className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
									/>
								</div>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
								<input
									type="email"
									value={userForm.email || ""}
									onChange={(e) => setUserForm((prev) => ({ ...prev, email: e.target.value }))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Téléphone</label>
								<input
									type="tel"
									value={userForm.phone || ""}
									onChange={(e) => setUserForm((prev) => ({ ...prev, phone: e.target.value }))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Rôle *</label>
								<select
									value={userForm.role || "guide"}
									onChange={(e) => setUserForm((prev) => ({ ...prev, role: e.target.value }))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								>
									<option value="guide">Guide</option>
									<option value="instructor">Instructeur</option>
									<option value="captain">Capitaine</option>
									<option value="manager">Manager</option>
								</select>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">Taux horaire (€)</label>
								<input
									type="number"
									step="0.01"
									value={userForm.hourly_rate || ""}
									onChange={(e) => setUserForm((prev) => ({ ...prev, hourly_rate: parseFloat(e.target.value) || 0 }))}
									className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								/>
							</div>

							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									<input
										type="checkbox"
										checked={userForm.is_active ?? true}
										onChange={(e) => setUserForm((prev) => ({ ...prev, is_active: e.target.checked }))}
										className="mr-2"
									/>
									Employé actif
								</label>
							</div>
						</div>

						<div>
							<h3 className="text-lg font-semibold text-gray-900 mb-4">Permissions</h3>
							<div className="space-y-3 p-4 bg-gray-50 rounded-lg">
								{getPermissionsList(userForm.role || "employee").map((permission, index) => (
									<div key={index} className="flex items-center gap-2">
										<Shield className="h-4 w-4 text-emerald-600" />
										<span className="text-sm text-gray-700">{permission}</span>
									</div>
								))}
							</div>

							<div className="mt-4 p-4 bg-blue-50 rounded-lg">
								<h4 className="font-medium text-blue-900 mb-2">Description du rôle</h4>
								<p className="text-sm text-blue-800">
									{userForm.role === "admin" &&
										"Accès complet à toutes les fonctionnalités de l'administration."}
									{userForm.role === "manager" &&
										"Peut gérer les réservations, services et employés."}
									{userForm.role === "employee" && "Accès en lecture seule aux informations de base."}
								</p>
							</div>
						</div>
					</div>

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel}>
							Annuler
						</Button>
						<Button onClick={handleSave} disabled={saving}>
							{saving ? "Sauvegarde..." : (isCreating ? "Créer l'employé" : "Sauvegarder")}
						</Button>
					</div>
				</div>
			)}
			{/* Employees List */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200">
				<div className="p-6 border-b border-gray-200">
					<h2 className="text-xl font-bold text-gray-900">Employés ({filteredUsers.length})</h2>
				</div>

				<div className="divide-y divide-gray-200">
					{filteredUsers.length === 0 ? (
						<div className="p-12 text-center">
							<p className="text-gray-500">Aucun employé trouvé</p>
						</div>
					) : (
						filteredUsers.map((employee) => {
							const fullName = getFullName(employee);
							const initials = fullName.split(" ").map((n) => n[0]).join("").toUpperCase().slice(0, 2);

							return (
								<div key={employee.id} className="p-6">
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-4">
											<div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center">
												<span className="text-emerald-600 font-medium">{initials}</span>
											</div>
											<div>
												<div className="flex items-center gap-2">
													<h3 className="text-lg font-semibold text-gray-900">{fullName}</h3>
													{!employee.is_active && (
														<span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded-full">
															Inactif
														</span>
													)}
												</div>
												<p className="text-gray-600">{employee.email}</p>
												{employee.phone && (
													<p className="text-sm text-gray-500">{employee.phone}</p>
												)}
												<div className="flex items-center gap-2 mt-1">
													<span
														className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(
															employee.role
														)}`}
													>
														{getRoleText(employee.role)}
													</span>
													{employee.hourly_rate && (
														<span className="text-xs text-gray-500">
															€{employee.hourly_rate}/h
														</span>
													)}
													{employee.qualifications && employee.qualifications.length > 0 && (
														<span className="text-xs text-gray-500">
															Qualifications: {employee.qualifications.join(", ")}
														</span>
													)}
												</div>
											</div>
										</div>

										<div className="flex gap-2">
											<Button variant="outline" size="sm" onClick={() => handleEdit(employee)} icon={Edit}>
												Modifier
											</Button>
											<Button
												variant="outline"
												size="sm"
												onClick={() => handleDelete(employee.id)}
												icon={Trash2}
												className="text-red-600 border-red-200 hover:bg-red-50"
												disabled={saving}
											>
												Supprimer
											</Button>
										</div>
									</div>
								</div>
							);
						})
					)}
			</div>

			{/* Role Permissions Info */}
			<div className="mt-8 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Permissions par rôle</h3>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
					<div className="p-4 bg-red-50 rounded-lg">
						<div className="flex items-center gap-2 mb-2">
							<Shield className="h-5 w-5 text-red-600" />
							<span className="font-medium text-red-900">Administrateur</span>
						</div>
						<ul className="text-sm text-red-800 space-y-1">
							<li>• Accès complet</li>
							<li>• Gestion des utilisateurs</li>
							<li>• Configuration système</li>
							<li>• Tous les rapports</li>
						</ul>
					</div>

					<div className="p-4 bg-blue-50 rounded-lg">
						<div className="flex items-center gap-2 mb-2">
							<Shield className="h-5 w-5 text-blue-600" />
							<span className="font-medium text-blue-900">Manager</span>
						</div>
						<ul className="text-sm text-blue-800 space-y-1">
							<li>• Gestion des réservations</li>
							<li>• Gestion des services</li>
							<li>• Gestion des employés</li>
							<li>• Rapports opérationnels</li>
						</ul>
					</div>

					<div className="p-4 bg-green-50 rounded-lg">
						<div className="flex items-center gap-2 mb-2">
							<Shield className="h-5 w-5 text-green-600" />
							<span className="font-medium text-green-900">Employé</span>
						</div>
						<ul className="text-sm text-green-800 space-y-1">
							<li>• Consultation des réservations</li>
							<li>• Consultation du planning</li>
							<li>• Mise à jour du profil</li>
							<li>• Rapports personnels</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
		</div>
	);
};

export default AdminUsers;
